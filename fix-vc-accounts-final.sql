-- 最终修复VC账号脚本
-- 彻底解决重复数据和负责人分配问题

-- ========================================
-- 步骤1: 备份和清理重复数据
-- ========================================
SELECT '=== 步骤1: 检查当前数据状况 ===' as message;

-- 检查总数
SELECT 
    COUNT(*) as total_accounts,
    COUNT(CASE WHEN creator = 'system' AND DATE(create_time) = CURDATE() THEN 1 END) as today_created
FROM vc_account 
WHERE deleted = 0;

-- 检查重复的account_code
SELECT '=== 重复的account_code ===' as message;
SELECT 
    account_code,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id ORDER BY id) as ids
FROM vc_account 
WHERE deleted = 0
GROUP BY account_code
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- ========================================
-- 步骤2: 删除今天创建的所有system记录（重新开始）
-- ========================================
SELECT '=== 步骤2: 删除今天创建的system记录 ===' as message;

-- 先显示要删除的记录
SELECT 
    id,
    account_code,
    account_name,
    manager_user_name,
    create_time
FROM vc_account 
WHERE creator = 'system' 
  AND DATE(create_time) = CURDATE()
  AND deleted = 0
ORDER BY id;

-- 删除今天创建的system记录
UPDATE vc_account 
SET deleted = 1, 
    updater = 'system-cleanup',
    update_time = NOW()
WHERE creator = 'system' 
  AND DATE(create_time) = CURDATE()
  AND deleted = 0;

SELECT CONCAT('标记删除的记录数量: ', ROW_COUNT()) as cleanup_result;

-- ========================================
-- 步骤3: 准备正确的VC账号数据
-- ========================================
SELECT '=== 步骤3: 准备正确的VC账号数据 ===' as message;

-- 创建正确的VC账号数据表
DROP TEMPORARY TABLE IF EXISTS correct_vc_data;
CREATE TEMPORARY TABLE correct_vc_data (
    account_code VARCHAR(100) PRIMARY KEY,
    account_name VARCHAR(200),
    manager_user_id BIGINT,
    manager_user_name VARCHAR(100),
    account_type VARCHAR(50) DEFAULT '专营店',
    priority_level INT DEFAULT 2
);

-- 插入正确的数据
INSERT INTO correct_vc_data (account_code, account_name, manager_user_id, manager_user_name) VALUES
-- 丁凯负责的VC账号 (用户ID: 142)
('xalbh668', '西安隆博海供应链', 142, '丁凯'),
('lylw668', '绿叶绿网668', 142, '丁凯'),
('jzgyl888', '建筑工业联888', 142, '丁凯'),
('zzktsm', '郑州柯鹏', 142, '丁凯'),
('qgy668', '武汉福莱达888', 142, '丁凯'),
('aet888666', '爱尔特888666', 142, '丁凯'),
('jd85824565', '梅派', 142, '丁凯'),
('nblqpop', '宁波沥泉贸易', 142, '丁凯'),
('ywhc', '义乌海程', 142, '丁凯'),
('shygj002', '上海御工匠', 142, '丁凯'),
('hnhk888', '河南华科888', 142, '丁凯'),
('mt888', '美迢', 142, '丁凯'),
('jfg8899', '建锋钢8899', 142, '丁凯'),
('hngs001', '河南钢隋001', 142, '丁凯'),
('jxyw666', '江西亿网666', 142, '丁凯'),
('hdvc', '撼顿vc运营', 142, '丁凯'),
('glqj', '恭良清洁用品', 142, '丁凯'),
('sdgz', '山东工筑电子', 142, '丁凯'),
('szdr888', '深圳德润888', 142, '丁凯'),
('drsmb', '鼎荣SMB', 142, '丁凯'),
('guohao123179', '国豪贸易', 142, '丁凯'),
('hq2022', '虎雀2022', 142, '丁凯'),
('shsla', '上海圣兰奥电子商务', 142, '丁凯'),
('zdh01', '做的好01', 142, '丁凯'),
('ztdz', '筑唐电子', 142, '丁凯'),
('jh08', '敬禾08', 142, '丁凯'),
('gnzp01', '钢念臻品01', 142, '丁凯'),
('shxy01', '上海锡豫01', 142, '丁凯'),
('cpkp', '车品柯鹏', 142, '丁凯'),

-- 李明磊负责的VC账号 (用户ID: 143)
('jfgjdjj', '建锋钢家电家居事业部', 143, '李明磊'),
('lh2255', '龙恒2255', 143, '李明磊'),
('jfgdschc', '建锋钢大商超慧采', 143, '李明磊'),
('ys3c02', '云盛3C数码02', 143, '李明磊'),
('zsysqc', '中山云盛汽车事业部', 143, '李明磊'),
('zsysdschc', '中山云盛大商超慧采', 143, '李明磊'),
('zzktsm2', '郑州旷田商贸', 143, '李明磊'),
('hcjdjj', '慧采家电家居', 143, '李明磊'),

-- 张光烁负责的VC账号 (用户ID: 144)
('shxzy002', '上海新智源002', 144, '张光烁'),
('hcdzsw', '海程电子商务', 144, '张光烁'),
('jyjc888', '隽佑建材888', 144, '张光烁'),
('qw78899', '千唯78899', 144, '张光烁'),
('mc459', '米层459', 144, '张光烁'),
('mchwhc', '米层户外慧采', 144, '张光烁'),
('fld3c', '福莱达3C数码', 144, '张光烁'),
('flddsf', '福莱达大时尚', 144, '张光烁'),
('mcjdjj', '米层家电家居', 144, '张光烁'),

-- 郭梦含负责的VC账号 (用户ID: 145)
('sfldsc', '赛帆禄大商超', 145, '郭梦含'),
('sfldqc', '赛帆禄大汽车', 145, '郭梦含'),
('sfl3c', '赛帆禄3C', 145, '郭梦含'),
('lwqchc', '莱维汽车慧采', 145, '郭梦含'),
('ldqchc', '麟道汽车慧采', 145, '郭梦含'),
('fqqchc', '丰谦汽车慧采', 145, '郭梦含'),
('shld', '上海麟道', 145, '郭梦含'),
('lbhnzhc', '隆博海农资慧采', 145, '郭梦含'),
('hnsflqc', '河南赛帆禄汽车', 145, '郭梦含'),
('yhtccp', '云汉天成车品供应', 145, '郭梦含'),

-- 曹嘉处负责的VC账号 (用户ID: 147)
('ht9988166', '海棠9988166', 147, '曹嘉处'),
('hnzy', '河南赞一', 147, '曹嘉处'),
('hngs', '河南钢隋', 147, '曹嘉处'),
('hndlhc', '河南迪黎慧采', 147, '曹嘉处'),
('shsla6688', '上海圣兰奥6688', 147, '曹嘉处'),
('hndl', '河南迪黎', 147, '曹嘉处'),
('gsqcsyhc', '钢隋汽车事业部慧采', 147, '曹嘉处'),
('slajj', '圣兰奥家居', 147, '曹嘉处'),
('qbzqchc', '齐步走汽车慧采', 147, '曹嘉处'),
('hnzyqchc', '河南赞一汽车慧采', 147, '曹嘉处');

SELECT CONCAT('准备的VC账号数据: ', COUNT(*), '条') as data_prepared FROM correct_vc_data;

-- ========================================
-- 步骤4: 处理现有数据
-- ========================================
SELECT '=== 步骤4: 更新现有VC账号 ===' as message;

-- 更新已存在的VC账号
UPDATE vc_account va
INNER JOIN correct_vc_data cvd ON va.account_code = cvd.account_code
SET 
    va.account_name = cvd.account_name,
    va.manager_user_id = cvd.manager_user_id,
    va.manager_user_name = cvd.manager_user_name,
    va.account_type = cvd.account_type,
    va.priority_level = cvd.priority_level,
    va.updater = 'system-fix',
    va.update_time = NOW()
WHERE va.deleted = 0;

SELECT CONCAT('更新现有VC账号数量: ', ROW_COUNT()) as update_count;

-- ========================================
-- 步骤5: 创建新的VC账号
-- ========================================
SELECT '=== 步骤5: 创建新的VC账号 ===' as message;

-- 创建不存在的VC账号
INSERT INTO vc_account (
    account_code,
    account_name,
    account_type,
    manager_user_id,
    manager_user_name,
    status,
    priority_level,
    creator,
    create_time,
    updater,
    update_time,
    tenant_id,
    deleted
)
SELECT 
    cvd.account_code,
    cvd.account_name,
    cvd.account_type,
    cvd.manager_user_id,
    cvd.manager_user_name,
    1 as status,
    cvd.priority_level,
    'system-fix' as creator,
    NOW() as create_time,
    'system-fix' as updater,
    NOW() as update_time,
    1 as tenant_id,
    0 as deleted
FROM correct_vc_data cvd
WHERE NOT EXISTS (
    SELECT 1 FROM vc_account va 
    WHERE va.account_code = cvd.account_code 
    AND va.deleted = 0
);

SELECT CONCAT('新创建VC账号数量: ', ROW_COUNT()) as create_count;

-- ========================================
-- 步骤6: 最终验证
-- ========================================
SELECT '=== 步骤6: 最终验证结果 ===' as message;

-- 验证所有VC账号
SELECT 
    va.account_code,
    va.account_name,
    va.manager_user_id,
    va.manager_user_name,
    va.status,
    va.priority_level,
    va.update_time
FROM vc_account va
INNER JOIN correct_vc_data cvd ON va.account_code = cvd.account_code
WHERE va.deleted = 0
ORDER BY va.manager_user_name, va.account_code;

-- 统计每个负责人的账号数量
SELECT '=== 负责人账号统计 ===' as message;
SELECT 
    va.manager_user_name,
    va.manager_user_id,
    COUNT(*) as account_count
FROM vc_account va
INNER JOIN correct_vc_data cvd ON va.account_code = cvd.account_code
WHERE va.deleted = 0
GROUP BY va.manager_user_name, va.manager_user_id
ORDER BY va.manager_user_name;

-- 检查是否还有重复
SELECT '=== 检查重复记录 ===' as message;
SELECT 
    account_code,
    COUNT(*) as count
FROM vc_account 
WHERE deleted = 0
GROUP BY account_code
HAVING COUNT(*) > 1;

-- 总计统计
SELECT '=== 总计统计 ===' as message;
SELECT 
    COUNT(*) as total_active_accounts,
    COUNT(CASE WHEN manager_user_id IN (142,143,144,145,147) THEN 1 END) as managed_accounts
FROM vc_account 
WHERE deleted = 0;

-- 清理临时表
DROP TEMPORARY TABLE correct_vc_data;

SELECT '=== 修复完成 ===' as message;
