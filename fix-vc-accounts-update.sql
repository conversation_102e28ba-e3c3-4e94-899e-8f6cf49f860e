-- 修正VC账号脚本 - 更新已存在的记录，创建不存在的记录
-- 解决重复数据和更新问题

-- ========================================
-- 步骤1: 检查当前VC账号情况
-- ========================================
SELECT '=== 检查当前VC账号数量 ===' as message;
SELECT COUNT(*) as total_accounts FROM vc_account WHERE deleted = 0;

SELECT '=== 检查今天创建的重复记录 ===' as message;
SELECT 
    account_code,
    account_name,
    manager_user_name,
    COUNT(*) as duplicate_count
FROM vc_account 
WHERE creator = 'system' 
  AND DATE(create_time) = CURDATE()
  AND deleted = 0
GROUP BY account_code, account_name, manager_user_name
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- ========================================
-- 步骤2: 删除今天创建的重复记录（保留第一条）
-- ========================================
SELECT '=== 删除重复记录 ===' as message;

-- 删除重复的记录，只保留ID最小的那条
DELETE t1 FROM vc_account t1
INNER JOIN vc_account t2 
WHERE t1.id > t2.id 
  AND t1.account_code = t2.account_code 
  AND t1.creator = 'system'
  AND t1.manager_user_name = t2.manager_user_name
  AND DATE(t1.create_time) = CURDATE()
  AND DATE(t2.create_time) = CURDATE();

SELECT CONCAT('删除重复记录数量: ', ROW_COUNT()) as delete_result;

-- ========================================
-- 步骤3: 创建临时表存储正确的VC账号数据
-- ========================================
DROP TEMPORARY TABLE IF EXISTS temp_vc_data;

CREATE TEMPORARY TABLE temp_vc_data (
    account_code VARCHAR(100),
    account_name VARCHAR(200),
    manager_user_id BIGINT,
    manager_user_name VARCHAR(100)
);

-- 插入正确的VC账号数据（修正重复的account_code和account_name）
INSERT INTO temp_vc_data VALUES
-- 丁凯的VC账号 (ID: 142)
('xalbh668', '西安隆博海供应链', 142, '丁凯'),
('lylw668', '绿叶绿网', 142, '丁凯'),
('jzgyl888', '建筑工业联', 142, '丁凯'),
('zzktsm', '郑州柯鹏', 142, '丁凯'),
('qgy668', '武汉福莱达888', 142, '丁凯'),
('aet888666', '爱尔特', 142, '丁凯'),
('jd85824565', '梅派', 142, '丁凯'),
('nblqpop', '宁波沥泉贸易', 142, '丁凯'),
('ywhc', '义乌海程', 142, '丁凯'),
('shygj002', '上海御工匠', 142, '丁凯'),
('hnhk888', '河南华科', 142, '丁凯'),
('mt888', '美迢', 142, '丁凯'),
('jfg8899', '建锋钢8899', 142, '丁凯'),
('hngs001', '河南钢隋001', 142, '丁凯'),
('jxyw666', '江西亿网', 142, '丁凯'),
('hdvc', '撼顿vc运营', 142, '丁凯'),
('glqj', '恭良清洁用品', 142, '丁凯'),
('sdgz', '山东工筑电子', 142, '丁凯'),
('szdr888', '深圳德润', 142, '丁凯'),
('drsmb', '鼎荣SMB', 142, '丁凯'),
('guohao123179', '国豪贸易', 142, '丁凯'),
('hq2022', '虎雀2022', 142, '丁凯'),
('shsla', '上海圣兰奥电子商务', 142, '丁凯'),
('zdh01', '做的好01', 142, '丁凯'),
('ztdz', '筑唐电子', 142, '丁凯'),
('jh08', '敬禾08', 142, '丁凯'),
('gnzp01', '钢念臻品01', 142, '丁凯'),
('shxy01', '上海锡豫01', 142, '丁凯'),
('cpkp', '车品柯鹏', 142, '丁凯'),

-- 李明磊的VC账号 (ID: 143)
('jfgjdjj', '建锋钢家电家居事业部', 143, '李明磊'),
('lh2255', '龙恒2255', 143, '李明磊'),
('jfgdschc', '建锋钢大商超慧采', 143, '李明磊'),
('ys3c02', '云盛3C数码02', 143, '李明磊'),
('zsysqc', '中山云盛汽车事业部', 143, '李明磊'),
('zsysdschc', '中山云盛大商超慧采', 143, '李明磊'),
('zzktsm2', '郑州旷田商贸', 143, '李明磊'),
('hcjdjj', '慧采家电家居', 143, '李明磊'),

-- 张光烁的VC账号 (ID: 144)
('shxzy002', '上海新智源', 144, '张光烁'),
('hcdzsw', '海程电子商务', 144, '张光烁'),
('jyjc888', '隽佑建材888', 144, '张光烁'),
('qw78899', '千唯78899', 144, '张光烁'),
('mc459', '米层459', 144, '张光烁'),
('mchwhc', '米层户外慧采', 144, '张光烁'),
('fld3c', '福莱达3C数码', 144, '张光烁'),
('flddsf', '福莱达大时尚', 144, '张光烁'),
('mcjdjj', '米层家电家居', 144, '张光烁'),

-- 郭梦含的VC账号 (ID: 145)
('sfldsc', '赛帆禄大商超', 145, '郭梦含'),
('sfldqc', '赛帆禄大汽车', 145, '郭梦含'),
('sfl3c', '赛帆禄3C', 145, '郭梦含'),
('lwqchc', '莱维汽车慧采', 145, '郭梦含'),
('ldqchc', '麟道汽车慧采', 145, '郭梦含'),
('fqqchc', '丰谦汽车慧采', 145, '郭梦含'),
('shld', '上海麟道', 145, '郭梦含'),
('lbhnzhc', '隆博海农资慧采', 145, '郭梦含'),
('hnsflqc', '河南赛帆禄汽车', 145, '郭梦含'),
('yhtccp', '云汉天成车品供应', 145, '郭梦含'),

-- 曹嘉处的VC账号 (ID: 147)
('ht9988166', '海棠9988166', 147, '曹嘉处'),
('hnzy', '河南赞一', 147, '曹嘉处'),
('hngs', '河南钢隋', 147, '曹嘉处'),
('hndlhc', '河南迪黎慧采', 147, '曹嘉处'),
('shsla6688', '上海圣兰奥6688', 147, '曹嘉处'),
('hndl', '河南迪黎', 147, '曹嘉处'),
('gsqcsyhc', '钢隋汽车事业部慧采', 147, '曹嘉处'),
('slajj', '圣兰奥家居', 147, '曹嘉处'),
('qbzqchc', '齐步走汽车慧采', 147, '曹嘉处'),
('hnzyqchc', '河南赞一汽车慧采', 147, '曹嘉处');

-- ========================================
-- 步骤4: 更新已存在的VC账号负责人信息
-- ========================================
SELECT '=== 更新已存在的VC账号负责人信息 ===' as message;

UPDATE vc_account va
JOIN temp_vc_data t ON va.account_code = t.account_code
SET 
    va.account_name = t.account_name,
    va.manager_user_id = t.manager_user_id,
    va.manager_user_name = t.manager_user_name,
    va.updater = 'system',
    va.update_time = NOW()
WHERE va.deleted = 0;

SELECT CONCAT('更新已存在VC账号数量: ', ROW_COUNT()) as update_result;

-- ========================================
-- 步骤5: 创建不存在的VC账号
-- ========================================
SELECT '=== 创建不存在的VC账号 ===' as message;

INSERT INTO vc_account (
    account_code,
    account_name,
    account_type,
    manager_user_id,
    manager_user_name,
    status,
    priority_level,
    creator,
    create_time,
    updater,
    update_time,
    tenant_id,
    deleted
)
SELECT 
    t.account_code,
    t.account_name,
    '专营店' as account_type,
    t.manager_user_id,
    t.manager_user_name,
    1 as status,
    2 as priority_level,
    'system' as creator,
    NOW() as create_time,
    'system' as updater,
    NOW() as update_time,
    1 as tenant_id,
    0 as deleted
FROM temp_vc_data t
WHERE NOT EXISTS (
    SELECT 1 FROM vc_account va 
    WHERE va.account_code = t.account_code AND va.deleted = 0
);

SELECT CONCAT('新创建VC账号数量: ', ROW_COUNT()) as create_result;

-- ========================================
-- 步骤6: 验证最终结果
-- ========================================
SELECT '=== 验证最终结果 ===' as message;

-- 显示所有VC账号及其负责人
SELECT 
    account_code,
    account_name,
    manager_user_id,
    manager_user_name,
    status,
    update_time
FROM vc_account 
WHERE deleted = 0
  AND manager_user_id IN (142, 143, 144, 145, 147)
ORDER BY manager_user_name, account_code;

-- 统计每个负责人的账号数量
SELECT '=== 统计每个负责人的账号数量 ===' as message;
SELECT 
    manager_user_name,
    COUNT(*) as account_count
FROM vc_account 
WHERE deleted = 0
  AND manager_user_id IN (142, 143, 144, 145, 147)
GROUP BY manager_user_name, manager_user_id
ORDER BY manager_user_name;

-- 检查是否还有重复记录
SELECT '=== 检查重复记录 ===' as message;
SELECT 
    account_code,
    COUNT(*) as duplicate_count
FROM vc_account 
WHERE deleted = 0
GROUP BY account_code
HAVING COUNT(*) > 1;

-- 清理临时表
DROP TEMPORARY TABLE temp_vc_data;

SELECT '=== 脚本执行完成 ===' as message;
