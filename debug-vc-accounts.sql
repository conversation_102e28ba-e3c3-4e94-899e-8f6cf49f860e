-- 调试VC账号负责人设置问题的脚本

-- ========================================
-- 步骤1: 检查system_users表中的用户数据
-- ========================================
SELECT '=== 检查system_users表结构 ===' as message;
SHOW TABLES LIKE '%user%';

SELECT '=== 检查system_users表中的所有用户 ===' as message;
SELECT 
    id,
    username,
    nickname,
    status,
    deleted
FROM system_users 
WHERE deleted = 0
ORDER BY id;

-- ========================================
-- 步骤2: 查找可能的负责人用户
-- ========================================
SELECT '=== 查找包含负责人姓名的用户 ===' as message;
SELECT
    id,
    username,
    nickname,
    status
FROM system_users
WHERE deleted = 0
  AND status = 1
  AND (
    nickname LIKE '%丁凯%' OR username LIKE '%丁凯%' OR
    nickname LIKE '%李明磊%' OR username LIKE '%李明磊%' OR
    nickname LIKE '%张光烁%' OR username LIKE '%张光烁%' OR
    nickname LIKE '%郭梦含%' OR username LIKE '%郭梦含%' OR
    nickname LIKE '%曹嘉处%' OR username LIKE '%曹嘉处%' OR
    nickname LIKE '%孟雪倩%' OR username LIKE '%孟雪倩%'
  )
ORDER BY nickname;

-- ========================================
-- 步骤3: 检查现有VC账号
-- ========================================
SELECT '=== 检查现有VC账号数量 ===' as message;
SELECT COUNT(*) as total_vc_accounts FROM vc_account WHERE deleted = 0;

SELECT '=== 显示现有VC账号示例 ===' as message;
SELECT 
    id,
    account_code,
    account_name,
    manager_user_id,
    manager_user_name,
    status
FROM vc_account 
WHERE deleted = 0
ORDER BY id
LIMIT 10;

-- ========================================
-- 步骤4: 手动设置负责人ID（根据实际用户ID调整）
-- ========================================
SELECT '=== 手动设置负责人ID ===' as message;

-- 假设找到的用户ID，请根据上面查询结果手动调整这些ID
SET @dingkai_id = (SELECT id FROM system_users WHERE deleted = 0 AND status = 1 AND (nickname = '丁凯' OR nickname LIKE '%丁凯%') LIMIT 1);
SET @liminglei_id = (SELECT id FROM system_users WHERE deleted = 0 AND status = 1 AND (nickname = '李明磊' OR nickname LIKE '%李明磊%') LIMIT 1);
SET @zhangguangshuo_id = (SELECT id FROM system_users WHERE deleted = 0 AND status = 1 AND (nickname = '张光烁' OR nickname LIKE '%张光烁%') LIMIT 1);
SET @guomenghan_id = (SELECT id FROM system_users WHERE deleted = 0 AND status = 1 AND (nickname = '郭梦含' OR nickname LIKE '%郭梦含%') LIMIT 1);
SET @caojiachu_id = (SELECT id FROM system_users WHERE deleted = 0 AND status = 1 AND (nickname = '曹嘉处' OR nickname LIKE '%曹嘉处%') LIMIT 1);
SET @mengxueqian_id = (SELECT id FROM system_users WHERE deleted = 0 AND status = 1 AND (nickname = '孟雪倩' OR nickname LIKE '%孟雪倩%') LIMIT 1);

-- 显示找到的用户ID
SELECT 
    '丁凯' as manager_name, @dingkai_id as user_id
UNION ALL SELECT '李明磊', @liminglei_id
UNION ALL SELECT '张光烁', @zhangguangshuo_id  
UNION ALL SELECT '郭梦含', @guomenghan_id
UNION ALL SELECT '曹嘉处', @caojiachu_id
UNION ALL SELECT '孟雪倩', @mengxueqian_id;

-- ========================================
-- 步骤5: 创建VC账号（只有找到用户ID的才创建）
-- ========================================

-- 丁凯的VC账号
INSERT IGNORE INTO vc_account (account_code, account_name, account_type, manager_user_id, manager_user_name, status, priority_level, creator, tenant_id)
SELECT * FROM (
    SELECT 'xalbh668' as account_code, '西安隆博海供应链' as account_name, '专营店' as account_type, @dingkai_id as manager_user_id, '丁凯' as manager_user_name, 1 as status, 2 as priority_level, 'system' as creator, 1 as tenant_id
    UNION ALL SELECT 'lylw668', 'lylw668', '专营店', @dingkai_id, '丁凯', 1, 2, 'system', 1
    UNION ALL SELECT 'jzgyl888', 'jzgyl888', '专营店', @dingkai_id, '丁凯', 1, 2, 'system', 1
    UNION ALL SELECT 'zzktsm', '郑州柯鹏', '专营店', @dingkai_id, '丁凯', 1, 2, 'system', 1
    UNION ALL SELECT 'qgy668', '武汉福莱达888', '专营店', @dingkai_id, '丁凯', 1, 2, 'system', 1
    UNION ALL SELECT 'aet888666', 'aet888666', '专营店', @dingkai_id, '丁凯', 1, 2, 'system', 1
    UNION ALL SELECT 'jd85824565', '梅派', '专营店', @dingkai_id, '丁凯', 1, 2, 'system', 1
    UNION ALL SELECT 'nblqpop', '宁波沥泉贸易', '专营店', @dingkai_id, '丁凯', 1, 2, 'system', 1
    UNION ALL SELECT 'ywhc', '义乌海程', '专营店', @dingkai_id, '丁凯', 1, 2, 'system', 1
    UNION ALL SELECT 'shygj002', '上海御工匠', '专营店', @dingkai_id, '丁凯', 1, 2, 'system', 1
) t WHERE @dingkai_id IS NOT NULL;

-- 李明磊的VC账号
INSERT IGNORE INTO vc_account (account_code, account_name, account_type, manager_user_id, manager_user_name, status, priority_level, creator, tenant_id)
SELECT * FROM (
    SELECT 'jfgjdjj' as account_code, '建锋钢家电家居事业部' as account_name, '专营店' as account_type, @liminglei_id as manager_user_id, '李明磊' as manager_user_name, 1 as status, 2 as priority_level, 'system' as creator, 1 as tenant_id
    UNION ALL SELECT 'lh2255', '龙恒2255', '专营店', @liminglei_id, '李明磊', 1, 2, 'system', 1
    UNION ALL SELECT 'jfgdschc', '建锋钢大商超慧采', '专营店', @liminglei_id, '李明磊', 1, 2, 'system', 1
    UNION ALL SELECT 'ys3c02', '云盛3C数码02', '专营店', @liminglei_id, '李明磊', 1, 2, 'system', 1
) t WHERE @liminglei_id IS NOT NULL;

-- 张光烁的VC账号
INSERT IGNORE INTO vc_account (account_code, account_name, account_type, manager_user_id, manager_user_name, status, priority_level, creator, tenant_id)
SELECT * FROM (
    SELECT 'shxzy002' as account_code, 'shxzy002' as account_name, '专营店' as account_type, @zhangguangshuo_id as manager_user_id, '张光烁' as manager_user_name, 1 as status, 2 as priority_level, 'system' as creator, 1 as tenant_id
    UNION ALL SELECT 'hcdzsw', '海程电子商务', '专营店', @zhangguangshuo_id, '张光烁', 1, 2, 'system', 1
    UNION ALL SELECT 'jyjc888', '隽佑建材888', '专营店', @zhangguangshuo_id, '张光烁', 1, 2, 'system', 1
) t WHERE @zhangguangshuo_id IS NOT NULL;

-- 郭梦含的VC账号
INSERT IGNORE INTO vc_account (account_code, account_name, account_type, manager_user_id, manager_user_name, status, priority_level, creator, tenant_id)
SELECT * FROM (
    SELECT 'sfldsc' as account_code, '赛帆禄大商超' as account_name, '专营店' as account_type, @guomenghan_id as manager_user_id, '郭梦含' as manager_user_name, 1 as status, 2 as priority_level, 'system' as creator, 1 as tenant_id
    UNION ALL SELECT 'sfldqc', '赛帆禄大汽车', '专营店', @guomenghan_id, '郭梦含', 1, 2, 'system', 1
) t WHERE @guomenghan_id IS NOT NULL;

-- 曹嘉处的VC账号
INSERT IGNORE INTO vc_account (account_code, account_name, account_type, manager_user_id, manager_user_name, status, priority_level, creator, tenant_id)
SELECT * FROM (
    SELECT 'ht9988166' as account_code, '海棠9988166' as account_name, '专营店' as account_type, @caojiachu_id as manager_user_id, '曹嘉处' as manager_user_name, 1 as status, 2 as priority_level, 'system' as creator, 1 as tenant_id
    UNION ALL SELECT 'hnzy', '河南赞一', '专营店', @caojiachu_id, '曹嘉处', 1, 2, 'system', 1
) t WHERE @caojiachu_id IS NOT NULL;

-- ========================================
-- 步骤6: 验证结果
-- ========================================
SELECT '=== 验证创建结果 ===' as message;
SELECT 
    account_code,
    account_name,
    manager_user_id,
    manager_user_name,
    status,
    create_time
FROM vc_account 
WHERE creator = 'system' 
  AND DATE(create_time) = CURDATE()
ORDER BY manager_user_name, account_code;

SELECT '=== 统计结果 ===' as message;
SELECT 
    manager_user_name,
    COUNT(*) as account_count
FROM vc_account 
WHERE creator = 'system' 
  AND DATE(create_time) = CURDATE()
  AND deleted = 0
GROUP BY manager_user_name
ORDER BY manager_user_name;
