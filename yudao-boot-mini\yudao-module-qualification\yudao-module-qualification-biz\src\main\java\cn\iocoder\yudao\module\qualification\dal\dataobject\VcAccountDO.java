package cn.iocoder.yudao.module.qualification.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * VC账号管理 DO
 *
 * <AUTHOR>
 */
@TableName("vc_account")
@KeySequence("vc_account_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VcAccountDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    
    /**
     * VC账号编码（唯一标识）
     */
    private String accountCode;
    
    /**
     * VC账号名称
     */
    private String accountName;
    
    /**
     * 账号类型（如：旗舰店、专营店、代理商等）
     */
    private String accountType;
    
    /**
     * 公司名称
     */
    private String companyName;
    
    /**
     * 联系人
     */
    private String contactPerson;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 联系邮箱
     */
    private String contactEmail;
    
    /**
     * 经营范围
     */
    private String businessScope;
    
    /**
     * 所属区域编码
     */
    private String regionCode;
    
    /**
     * 所属区域名称
     */
    private String regionName;
    
    /**
     * 负责人用户ID（关联system_users表）
     */
    private Long managerUserId;
    
    /**
     * 负责人姓名（冗余字段）
     */
    private String managerUserName;
    
    /**
     * 备用负责人用户ID
     */
    private Long backupManagerUserId;
    
    /**
     * 备用负责人姓名
     */
    private String backupManagerUserName;
    
    /**
     * 状态（1正常 0停用）
     */
    private Integer status;
    
    /**
     * 优先级（1高 2中 3低）
     */
    private Integer priorityLevel;
    
    /**
     * 备注
     */
    private String remark;

    /**
     * 关联资质数量（非数据库字段，用于统计显示）
     */
    @TableField(exist = false)
    private Long qualificationCount;

    /**
     * 即将过期资质数量（非数据库字段，用于统计显示）
     */
    @TableField(exist = false)
    private Long expiringCount;

}
