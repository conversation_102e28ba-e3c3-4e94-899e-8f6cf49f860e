-- 修复VC账号管理权限分配问题的SQL脚本
-- 确保管理员角色拥有VC账号管理的所有权限

-- 1. 首先检查VC账号管理的菜单权限是否存在
SELECT 
    id,
    name,
    permission,
    type,
    parent_id
FROM `system_menu` 
WHERE id BETWEEN 3009 AND 3014 
  AND deleted = 0
ORDER BY id;

-- 2. 获取超级管理员角色ID（通常是1）
SET @admin_role_id = (SELECT id FROM system_role WHERE code = 'super_admin' AND deleted = 0 LIMIT 1);

-- 如果没有找到super_admin，尝试查找其他管理员角色
SET @admin_role_id = IFNULL(@admin_role_id, 
    (SELECT id FROM system_role WHERE name LIKE '%管理员%' AND deleted = 0 LIMIT 1)
);

-- 如果还是没有找到，使用ID=1（通常是超级管理员）
SET @admin_role_id = IFNULL(@admin_role_id, 1);

SELECT CONCAT('使用角色ID: ', @admin_role_id) as role_info;

-- 3. 为管理员角色分配VC账号管理权限（如果不存在）
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
SELECT @admin_role_id, 3009, 'admin', NOW(), 'admin', NOW(), 0, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `system_role_menu` 
    WHERE `role_id` = @admin_role_id AND `menu_id` = 3009 AND deleted = 0
);

INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
SELECT @admin_role_id, 3010, 'admin', NOW(), 'admin', NOW(), 0, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `system_role_menu` 
    WHERE `role_id` = @admin_role_id AND `menu_id` = 3010 AND deleted = 0
);

INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
SELECT @admin_role_id, 3011, 'admin', NOW(), 'admin', NOW(), 0, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `system_role_menu` 
    WHERE `role_id` = @admin_role_id AND `menu_id` = 3011 AND deleted = 0
);

INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
SELECT @admin_role_id, 3012, 'admin', NOW(), 'admin', NOW(), 0, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `system_role_menu` 
    WHERE `role_id` = @admin_role_id AND `menu_id` = 3012 AND deleted = 0
);

INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
SELECT @admin_role_id, 3013, 'admin', NOW(), 'admin', NOW(), 0, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `system_role_menu` 
    WHERE `role_id` = @admin_role_id AND `menu_id` = 3013 AND deleted = 0
);

INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
SELECT @admin_role_id, 3014, 'admin', NOW(), 'admin', NOW(), 0, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `system_role_menu` 
    WHERE `role_id` = @admin_role_id AND `menu_id` = 3014 AND deleted = 0
);

-- 4. 验证权限分配结果
SELECT 
    r.name as role_name,
    m.name as menu_name,
    m.permission,
    rm.create_time
FROM `system_role_menu` rm
JOIN `system_role` r ON rm.role_id = r.id
JOIN `system_menu` m ON rm.menu_id = m.id
WHERE rm.role_id = @admin_role_id 
  AND m.id BETWEEN 3009 AND 3014
  AND rm.deleted = 0
  AND r.deleted = 0
  AND m.deleted = 0
ORDER BY m.id;

-- 5. 检查当前登录用户是否有该角色权限
-- 注意：需要根据实际的用户ID进行查询
-- SELECT 
--     u.username,
--     r.name as role_name,
--     m.name as menu_name,
--     m.permission
-- FROM `system_user_role` ur
-- JOIN `system_user` u ON ur.user_id = u.id
-- JOIN `system_role` r ON ur.role_id = r.id
-- JOIN `system_role_menu` rm ON r.id = rm.role_id
-- JOIN `system_menu` m ON rm.menu_id = m.id
-- WHERE u.username = 'admin'  -- 替换为实际用户名
--   AND m.id BETWEEN 3009 AND 3014
--   AND ur.deleted = 0
--   AND u.deleted = 0
--   AND r.deleted = 0
--   AND rm.deleted = 0
--   AND m.deleted = 0
-- ORDER BY m.id;
