import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/qualification/summary',
    name: 'QualificationSummary',
    component: () => import('#/views/qualification/summary/index.vue'),
    meta: {
      title: '资质汇总',
      icon: 'ant-design:table-outlined',
      keepAlive: true,
      hideInMenu: true,
    },
  },
  {
    path: '/qualification/vc-account',
    name: 'VcAccount',
    component: () => import('#/views/qualification/vc-account/index.vue'),
    meta: {
      title: 'VC账号管理',
      icon: 'ant-design:user-outlined',
      keepAlive: true,
      hideInMenu: true,
    },
  },
]

export default routes
