-- 简单测试脚本，逐步检查数据

-- ========================================
-- 步骤1: 检查system_users表中的所有用户
-- ========================================
SELECT '=== 查看所有用户 ===' as message;
SELECT 
    id,
    username,
    nickname,
    status,
    deleted
FROM system_users 
WHERE deleted = 0
ORDER BY id;

-- ========================================
-- 步骤2: 分别查找每个负责人
-- ========================================
SELECT '=== 查找丁凯 ===' as message;
SELECT 
    id,
    username,
    nickname,
    status
FROM system_users 
WHERE deleted = 0 
  AND status = 1
  AND (nickname LIKE '%丁凯%' OR username LIKE '%丁凯%')
ORDER BY nickname;

SELECT '=== 查找李明磊 ===' as message;
SELECT 
    id,
    username,
    nickname,
    status
FROM system_users 
WHERE deleted = 0 
  AND status = 1
  AND (nickname LIKE '%李明磊%' OR username LIKE '%李明磊%')
ORDER BY nickname;

SELECT '=== 查找张光烁 ===' as message;
SELECT 
    id,
    username,
    nickname,
    status
FROM system_users 
WHERE deleted = 0 
  AND status = 1
  AND (nickname LIKE '%张光烁%' OR username LIKE '%张光烁%')
ORDER BY nickname;

SELECT '=== 查找郭梦含 ===' as message;
SELECT 
    id,
    username,
    nickname,
    status
FROM system_users 
WHERE deleted = 0 
  AND status = 1
  AND (nickname LIKE '%郭梦含%' OR username LIKE '%郭梦含%')
ORDER BY nickname;

SELECT '=== 查找曹嘉处 ===' as message;
SELECT 
    id,
    username,
    nickname,
    status
FROM system_users 
WHERE deleted = 0 
  AND status = 1
  AND (nickname LIKE '%曹嘉处%' OR username LIKE '%曹嘉处%')
ORDER BY nickname;

SELECT '=== 查找孟雪倩 ===' as message;
SELECT 
    id,
    username,
    nickname,
    status
FROM system_users 
WHERE deleted = 0 
  AND status = 1
  AND (nickname LIKE '%孟雪倩%' OR username LIKE '%孟雪倩%')
ORDER BY nickname;

-- ========================================
-- 步骤3: 检查现有VC账号
-- ========================================
SELECT '=== 检查现有VC账号数量 ===' as message;
SELECT COUNT(*) as total_vc_accounts FROM vc_account WHERE deleted = 0;

SELECT '=== 显示现有VC账号示例 ===' as message;
SELECT 
    id,
    account_code,
    account_name,
    manager_user_id,
    manager_user_name,
    status
FROM vc_account 
WHERE deleted = 0
ORDER BY id
LIMIT 5;
