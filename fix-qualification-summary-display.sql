-- 修复资质汇总页面显示问题的SQL脚本
-- 主要解决状态字段显示和操作按钮权限问题

-- 1. 添加 expired_status 字段（如果不存在）
-- 检查字段是否存在
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'qualification_summary' 
      AND COLUMN_NAME = 'expired_status'
);

-- 如果字段不存在，则添加
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE qualification_summary ADD COLUMN expired_status TINYINT(4) DEFAULT 1 COMMENT "资质过期状态（0已过期 1正常 2即将过期）"',
    'SELECT "expired_status字段已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 更新现有数据的 expired_status 字段
-- 根据资质到期时间自动计算并设置过期状态
UPDATE qualification_summary 
SET expired_status = CASE 
    WHEN qualification_expire_date < NOW() THEN 0  -- 已过期
    WHEN qualification_expire_date < DATE_ADD(NOW(), INTERVAL 30 DAY) THEN 2  -- 即将过期（30天内）
    ELSE 1  -- 正常
END
WHERE expired_status IS NULL OR expired_status = 1;

-- 3. 确保管理员角色拥有资质汇总的所有权限
-- 获取超级管理员角色ID
SET @admin_role_id = (SELECT id FROM system_role WHERE code = 'super_admin' AND deleted = 0 LIMIT 1);
SET @admin_role_id = IFNULL(@admin_role_id, 
    (SELECT id FROM system_role WHERE name LIKE '%管理员%' AND deleted = 0 LIMIT 1)
);
SET @admin_role_id = IFNULL(@admin_role_id, 1);

-- 为管理员角色分配资质汇总的所有权限（如果不存在）
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
SELECT @admin_role_id, 3001, 'admin', NOW(), 'admin', NOW(), 0, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `system_role_menu` 
    WHERE `role_id` = @admin_role_id AND `menu_id` = 3001 AND deleted = 0
);

INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
SELECT @admin_role_id, 3002, 'admin', NOW(), 'admin', NOW(), 0, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `system_role_menu` 
    WHERE `role_id` = @admin_role_id AND `menu_id` = 3002 AND deleted = 0
);

INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
SELECT @admin_role_id, 3003, 'admin', NOW(), 'admin', NOW(), 0, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `system_role_menu` 
    WHERE `role_id` = @admin_role_id AND `menu_id` = 3003 AND deleted = 0
);

INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
SELECT @admin_role_id, 3004, 'admin', NOW(), 'admin', NOW(), 0, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `system_role_menu` 
    WHERE `role_id` = @admin_role_id AND `menu_id` = 3004 AND deleted = 0
);

INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
SELECT @admin_role_id, 3005, 'admin', NOW(), 'admin', NOW(), 0, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `system_role_menu` 
    WHERE `role_id` = @admin_role_id AND `menu_id` = 3005 AND deleted = 0
);

INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
SELECT @admin_role_id, 3006, 'admin', NOW(), 'admin', NOW(), 0, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `system_role_menu` 
    WHERE `role_id` = @admin_role_id AND `menu_id` = 3006 AND deleted = 0
);

INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
SELECT @admin_role_id, 3007, 'admin', NOW(), 'admin', NOW(), 0, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `system_role_menu` 
    WHERE `role_id` = @admin_role_id AND `menu_id` = 3007 AND deleted = 0
);

-- 4. 验证修复结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'qualification_summary' 
  AND COLUMN_NAME IN ('status', 'expired_status')
ORDER BY COLUMN_NAME;

-- 5. 检查数据状态分布
SELECT 
    status,
    expired_status,
    COUNT(*) as count,
    CASE 
        WHEN status = 1 THEN '正常'
        WHEN status = 0 THEN '停用'
        ELSE '未知'
    END as status_text,
    CASE 
        WHEN expired_status = 0 THEN '已过期'
        WHEN expired_status = 1 THEN '正常'
        WHEN expired_status = 2 THEN '即将过期'
        ELSE '未知'
    END as expired_status_text
FROM qualification_summary 
WHERE deleted = 0
GROUP BY status, expired_status
ORDER BY status, expired_status;

-- 6. 验证权限分配
SELECT 
    r.name as role_name,
    m.name as menu_name,
    m.permission
FROM `system_role_menu` rm
JOIN `system_role` r ON rm.role_id = r.id
JOIN `system_menu` m ON rm.menu_id = m.id
WHERE rm.role_id = @admin_role_id 
  AND m.id BETWEEN 3001 AND 3007
  AND rm.deleted = 0
  AND r.deleted = 0
  AND m.deleted = 0
ORDER BY m.id;
