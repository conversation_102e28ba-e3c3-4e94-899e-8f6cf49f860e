-- 修复资质管理菜单重复问题的SQL脚本
-- 执行前请备份数据库

-- 1. 修正重复的菜单名称
UPDATE `system_menu` SET `name` = '资质汇总' WHERE `id` = 3001;

-- 2. 修正按钮权限名称
UPDATE `system_menu` SET `name` = '资质汇总查询' WHERE `id` = 3002;
UPDATE `system_menu` SET `name` = '资质汇总新增' WHERE `id` = 3003;
UPDATE `system_menu` SET `name` = '资质汇总修改' WHERE `id` = 3004;
UPDATE `system_menu` SET `name` = '资质汇总删除' WHERE `id` = 3005;
UPDATE `system_menu` SET `name` = '资质汇总导出' WHERE `id` = 3006;

-- 3. 添加VC账号管理菜单（如果不存在）
INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT 3009, 'VC账号管理', 'qualification:vc-account:query', 2, 2, 3000, 'vc-account', 'ant-design:user-outlined', 'qualification/vc-account/index', 'VcAccount', 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'
WHERE NOT EXISTS (SELECT 1 FROM `system_menu` WHERE `id` = 3009);

-- 4. 添加VC账号管理按钮权限（如果不存在）
INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT 3010, 'VC账号查询', 'qualification:vc-account:query', 3, 1, 3009, '', '', '', '', 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'
WHERE NOT EXISTS (SELECT 1 FROM `system_menu` WHERE `id` = 3010);

INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT 3011, 'VC账号新增', 'qualification:vc-account:create', 3, 2, 3009, '', '', '', '', 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'
WHERE NOT EXISTS (SELECT 1 FROM `system_menu` WHERE `id` = 3011);

INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT 3012, 'VC账号修改', 'qualification:vc-account:update', 3, 3, 3009, '', '', '', '', 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'
WHERE NOT EXISTS (SELECT 1 FROM `system_menu` WHERE `id` = 3012);

INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT 3013, 'VC账号删除', 'qualification:vc-account:delete', 3, 4, 3009, '', '', '', '', 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'
WHERE NOT EXISTS (SELECT 1 FROM `system_menu` WHERE `id` = 3013);

INSERT INTO `system_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT 3014, 'VC账号导出', 'qualification:vc-account:export', 3, 5, 3009, '', '', '', '', 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'
WHERE NOT EXISTS (SELECT 1 FROM `system_menu` WHERE `id` = 3014);

-- 5. 验证修复结果
SELECT 
    id,
    name,
    permission,
    type,
    sort,
    parent_id,
    path,
    component
FROM `system_menu` 
WHERE (id BETWEEN 3000 AND 3014) 
  AND deleted = 0
ORDER BY id;
