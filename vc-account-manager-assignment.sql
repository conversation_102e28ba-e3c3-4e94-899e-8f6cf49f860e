-- ========================================
-- VC账号负责人分配脚本
-- 功能：根据提供的VC账号和负责人信息，自动创建VC账号并分配负责人
-- 创建时间：2025-01-05
-- ========================================

-- 开启事务处理
START TRANSACTION;

-- 设置SQL模式，避免严格模式下的问题
SET SESSION sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

-- ========================================
-- 步骤1：创建临时表存储VC账号和负责人映射关系
-- ========================================
DROP TEMPORARY TABLE IF EXISTS temp_vc_manager_mapping;
CREATE TEMPORARY TABLE temp_vc_manager_mapping (
    account_name VARCHAR(200) NOT NULL COMMENT 'VC账号名称',
    manager_name VARCHAR(100) NOT NULL COMMENT '负责人姓名',
    account_code VARCHAR(100) NULL COMMENT 'VC账号编码（自动生成）',
    manager_user_id BIGINT(20) NULL COMMENT '负责人用户ID',
    INDEX idx_manager_name (manager_name),
    INDEX idx_account_name (account_name)
) ENGINE=MEMORY COMMENT='VC账号负责人映射临时表';

-- ========================================
-- 步骤2：插入VC账号和负责人映射数据
-- ========================================

-- 丁凯负责的VC账号
INSERT INTO temp_vc_manager_mapping (account_name, manager_name) VALUES
('西安隆博海供应链', '丁凯'),
('lylw668', '丁凯'),
('jzgyl888', '丁凯'),
('郑州柯鹏', '丁凯'),
('武汉福莱达888', '丁凯'),
('aet888666', '丁凯'),
('梅派', '丁凯'),
('jd85824565', '丁凯'),
('宁波沥泉贸易', '丁凯'),
('义乌海程', '丁凯'),
('上海御工匠', '丁凯'),
('hnhk888', '丁凯'),
('美迢', '丁凯'),
('建锋钢8899', '丁凯'),
('河南钢隋001', '丁凯'),
('jxyw666', '丁凯'),
('撼顿vc运营', '丁凯'),
('恭良清洁用品', '丁凯'),
('山东工筑电子', '丁凯'),
('szdr888', '丁凯'),
('鼎荣SMB', '丁凯'),
('guohao123179', '丁凯'),
('虎雀2022', '丁凯'),
('上海圣兰奥电子商务', '丁凯'),
('做的好01', '丁凯'),
('筑唐电子', '丁凯'),
('敬禾08', '丁凯'),
('钢念臻品01', '丁凯'),
('上海锡豫01', '丁凯'),
('车品柯鹏', '丁凯');

-- 李明磊负责的VC账号
INSERT INTO temp_vc_manager_mapping (account_name, manager_name) VALUES
('xalbh668', '李明磊'),
('zzktsm', '李明磊'),
('qgy668', '李明磊'),
('撼地pop', '李明磊'),
('撼地工业pop', '李明磊'),
('金佩奇工业品pop', '李明磊'),
('钢隋家装工具pop', '李明磊'),
('隆博海清洁pop', '李明磊'),
('金佩奇专卖pop', '李明磊'),
('柯鹏仪表pop', '李明磊'),
('hngspop', '李明磊'),
('钢隋开关pop', '李明磊'),
('莱维pop', '李明磊'),
('nblqpop', '李明磊'),
('隆博海阀门pop', '李明磊'),
('钢隋工业品工具pop', '李明磊'),
('颂造工业品pop', '李明磊'),
('富仪官方旗舰店', '李明磊'),
('析禾仪表', '李明磊'),
('建锋钢家电家居事业部', '李明磊'),
('龙恒2255', '李明磊');

-- 张光烁负责的VC账号
INSERT INTO temp_vc_manager_mapping (account_name, manager_name) VALUES
('建锋钢大商超慧采', '张光烁'),
('云盛3C数码02', '张光烁'),
('中山云盛汽车事业部', '张光烁'),
('中山云盛大商超慧采', '张光烁'),
('郑州旷田商贸', '张光烁'),
('慧采家电家居', '张光烁');

-- 郭梦含负责的VC账号
INSERT INTO temp_vc_manager_mapping (account_name, manager_name) VALUES
('shxzy002', '郭梦含'),
('shygj002', '郭梦含'),
('海程电子商务', '郭梦含'),
('隽佑建材888', '郭梦含'),
('千唯78899', '郭梦含'),
('米层459', '郭梦含'),
('米层户外慧采', '郭梦含'),
('福莱达3C数码', '郭梦含'),
('福莱达大时尚', '郭梦含'),
('米层家电家居', '郭梦含');

-- 曹嘉处负责的VC账号
INSERT INTO temp_vc_manager_mapping (account_name, manager_name) VALUES
('赛帆禄大商超', '曹嘉处'),
('赛帆禄大汽车', '曹嘉处'),
('赛帆禄3C', '曹嘉处'),
('莱维汽车慧采', '曹嘉处'),
('麟道汽车慧采', '曹嘉处'),
('丰谦汽车慧采', '曹嘉处'),
('上海麟道', '曹嘉处'),
('隆博海农资慧采', '曹嘉处'),
('河南赛帆禄汽车', '曹嘉处'),
('云汉天成车品供应', '曹嘉处');

-- 孟雪倩负责的VC账号
INSERT INTO temp_vc_manager_mapping (account_name, manager_name) VALUES
('海棠9988166', '孟雪倩'),
('河南赞一', '孟雪倩'),
('河南钢隋', '孟雪倩'),
('河南迪黎慧采', '孟雪倩'),
('上海圣兰奥6688', '孟雪倩'),
('河南迪黎', '孟雪倩'),
('钢隋汽车事业部慧采', '孟雪倩'),
('圣兰奥家居', '孟雪倩'),
('齐步走汽车慧采', '孟雪倩'),
('河南赞一汽车慧采', '孟雪倩');

-- ========================================
-- 步骤3：设置VC账号编码并查找负责人用户ID
-- ========================================

-- 设置VC账号编码（直接使用中文账号名称作为编码）
UPDATE temp_vc_manager_mapping
SET account_code = account_name;

-- 查找并更新负责人用户ID
UPDATE temp_vc_manager_mapping t
INNER JOIN system_users u ON (
    CONVERT(u.nickname USING utf8mb4) = CONVERT(t.manager_name USING utf8mb4)
    OR CONVERT(u.username USING utf8mb4) = CONVERT(t.manager_name USING utf8mb4)
) AND u.deleted = 0 AND u.status = 0
SET t.manager_user_id = u.id;

-- ========================================
-- 步骤4：验证数据准备情况
-- ========================================
SELECT '=== 数据准备验证 ===' as message;

-- 检查是否所有负责人都找到了对应的用户ID
SELECT 
    manager_name,
    COUNT(*) as account_count,
    COUNT(manager_user_id) as found_user_count,
    CASE WHEN COUNT(manager_user_id) = 0 THEN '❌ 未找到用户' ELSE '✅ 已找到用户' END as status
FROM temp_vc_manager_mapping 
GROUP BY manager_name, manager_user_id
ORDER BY manager_name;

-- 如果有负责人未找到用户ID，显示详细信息
SELECT '=== 未找到用户ID的负责人 ===' as message;
SELECT DISTINCT manager_name
FROM temp_vc_manager_mapping 
WHERE manager_user_id IS NULL;

-- ========================================
-- 步骤5：处理VC账号数据（创建或更新）
-- ========================================

-- 5.1 创建不存在的VC账号
INSERT INTO vc_account (
    account_code,
    account_name,
    account_type,
    manager_user_id,
    manager_user_name,
    status,
    priority_level,
    creator,
    create_time,
    updater,
    update_time,
    deleted,
    tenant_id
)
SELECT 
    t.account_code,
    t.account_name,
    'VC账号' as account_type,
    t.manager_user_id,
    t.manager_name as manager_user_name,
    1 as status,
    1 as priority_level,
    'system' as creator,
    NOW() as create_time,
    'system' as updater,
    NOW() as update_time,
    0 as deleted,
    1 as tenant_id
FROM temp_vc_manager_mapping t
WHERE t.manager_user_id IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM vc_account va
      WHERE CONVERT(va.account_code USING utf8mb4) = CONVERT(t.account_code USING utf8mb4)
        AND va.deleted = 0
        AND va.tenant_id = 1
  );

-- 5.2 更新已存在但负责人信息不完整的VC账号
UPDATE vc_account va
INNER JOIN temp_vc_manager_mapping t ON va.account_code = t.account_code
SET 
    va.manager_user_id = t.manager_user_id,
    va.manager_user_name = t.manager_name,
    va.updater = 'system',
    va.update_time = NOW()
WHERE va.deleted = 0 
  AND va.tenant_id = 1
  AND t.manager_user_id IS NOT NULL
  AND (va.manager_user_id IS NULL OR va.manager_user_id != t.manager_user_id);

-- ========================================
-- 步骤6：执行结果验证
-- ========================================
SELECT '=== 执行结果验证 ===' as message;

-- 统计本次处理的VC账号数量
SELECT 
    '本次处理统计' as category,
    COUNT(*) as total_accounts,
    COUNT(CASE WHEN manager_user_id IS NOT NULL THEN 1 END) as assigned_accounts,
    COUNT(CASE WHEN manager_user_id IS NULL THEN 1 END) as unassigned_accounts
FROM temp_vc_manager_mapping;

-- 按负责人统计分配的VC账号数量
SELECT 
    t.manager_name,
    COUNT(*) as total_mapped,
    COUNT(va.id) as created_or_updated,
    CONCAT(ROUND(COUNT(va.id) * 100.0 / COUNT(*), 1), '%') as success_rate
FROM temp_vc_manager_mapping t
LEFT JOIN vc_account va ON va.account_code = t.account_code AND va.deleted = 0
WHERE t.manager_user_id IS NOT NULL
GROUP BY t.manager_name
ORDER BY t.manager_name;

-- 显示创建或更新的VC账号详情（最近10条）
SELECT 
    va.account_code,
    va.account_name,
    va.manager_user_name,
    va.status,
    va.create_time,
    va.update_time,
    CASE 
        WHEN va.create_time = va.update_time THEN '新创建'
        ELSE '已更新'
    END as operation_type
FROM vc_account va
INNER JOIN temp_vc_manager_mapping t ON va.account_code = t.account_code
WHERE va.deleted = 0 
  AND va.tenant_id = 1
  AND (DATE(va.create_time) = CURDATE() OR DATE(va.update_time) = CURDATE())
ORDER BY va.update_time DESC
LIMIT 10;

-- ========================================
-- 步骤7：错误处理和回滚检查
-- ========================================

-- 检查是否有重复的account_code
SELECT '=== 重复账号编码检查 ===' as message;
SELECT 
    account_code,
    COUNT(*) as duplicate_count
FROM vc_account 
WHERE deleted = 0 AND tenant_id = 1
GROUP BY account_code 
HAVING COUNT(*) > 1;

-- 检查是否有无效的manager_user_id
SELECT '=== 无效负责人ID检查 ===' as message;
SELECT 
    va.account_code,
    va.account_name,
    va.manager_user_id,
    va.manager_user_name
FROM vc_account va
LEFT JOIN system_users u ON va.manager_user_id = u.id AND u.deleted = 0
WHERE va.deleted = 0 
  AND va.tenant_id = 1
  AND va.manager_user_id IS NOT NULL 
  AND u.id IS NULL;

-- 如果发现严重错误，可以手动执行 ROLLBACK; 来回滚事务
-- 否则执行 COMMIT; 来提交事务

-- ========================================
-- 提交事务（如果一切正常）
-- ========================================
COMMIT;

SELECT '=== VC账号负责人分配完成 ===' as message;
SELECT CONCAT('处理时间: ', NOW()) as completion_time;

-- ========================================
-- 清理临时表
-- ========================================
DROP TEMPORARY TABLE IF EXISTS temp_vc_manager_mapping;

-- ========================================
-- 脚本执行说明
-- ========================================
/*
使用说明：
1. 本脚本使用事务处理，确保数据一致性
2. 如果执行过程中发现错误，事务会自动回滚
3. account_code和account_name都使用原始中文账号名称，不进行编码转换
4. 只有在system_users表中找到对应用户的VC账号才会被处理
5. 脚本执行完成后会显示详细的验证结果

注意事项：
1. 请确保system_users表中存在对应的负责人用户
2. 请确保vc_account表已经创建并具有正确的表结构
3. 建议在测试环境中先执行验证
4. 如果需要回滚，请在COMMIT之前执行ROLLBACK;
*/
