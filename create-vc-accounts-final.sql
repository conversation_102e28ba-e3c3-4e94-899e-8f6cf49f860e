-- 最终VC账号创建脚本
-- 根据实际用户ID创建VC账号

-- ========================================
-- 步骤1: 确认负责人用户ID
-- ========================================
SELECT '=== 确认负责人用户ID ===' as message;
SELECT 
    id,
    username,
    nickname,
    status
FROM system_users 
WHERE id IN (142, 143, 144, 145, 146, 147)
ORDER BY id;

-- ========================================
-- 步骤2: 创建VC账号（使用实际用户ID）
-- ========================================
SELECT '=== 开始创建VC账号 ===' as message;

-- 丁凯的VC账号 (ID: 142)
INSERT IGNORE INTO vc_account (account_code, account_name, account_type, manager_user_id, manager_user_name, status, priority_level, creator, create_time, updater, update_time, tenant_id, deleted)
VALUES 
('xalbh668', '西安隆博海供应链', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('lylw668', 'lylw668', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('jzgyl888', 'jzgyl888', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('zzktsm', '郑州柯鹏', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('qgy668', '武汉福莱达888', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('aet888666', 'aet888666', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('jd85824565', '梅派', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('nblqpop', '宁波沥泉贸易', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('ywhc', '义乌海程', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('shygj002', '上海御工匠', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('hnhk888', 'hnhk888', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('mt888', '美迢', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('jfg8899', '建锋钢8899', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('hngs001', '河南钢隋001', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('jxyw666', 'jxyw666', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('hdvc', '撼顿vc运营', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('glqj', '恭良清洁用品', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('sdgz', '山东工筑电子', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('szdr888', 'szdr888', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('drsmb', '鼎荣SMB', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('guohao123179', 'guohao123179', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('hq2022', '虎雀2022', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('shsla', '上海圣兰奥电子商务', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('zdh01', '做的好01', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('ztdz', '筑唐电子', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('jh08', '敬禾08', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('gnzp01', '钢念臻品01', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('shxy01', '上海锡豫01', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('cpkp', '车品柯鹏', '专营店', 142, '丁凯', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0);

SELECT CONCAT('丁凯的VC账号创建完成，影响行数: ', ROW_COUNT()) as result;

-- 李明磊的VC账号 (ID: 143)
INSERT IGNORE INTO vc_account (account_code, account_name, account_type, manager_user_id, manager_user_name, status, priority_level, creator, create_time, updater, update_time, tenant_id, deleted)
VALUES 
('jfgjdjj', '建锋钢家电家居事业部', '专营店', 143, '李明磊', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('lh2255', '龙恒2255', '专营店', 143, '李明磊', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('jfgdschc', '建锋钢大商超慧采', '专营店', 143, '李明磊', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('ys3c02', '云盛3C数码02', '专营店', 143, '李明磊', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('zsysqc', '中山云盛汽车事业部', '专营店', 143, '李明磊', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('zsysdschc', '中山云盛大商超慧采', '专营店', 143, '李明磊', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('zzktsm2', '郑州旷田商贸', '专营店', 143, '李明磊', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('hcjdjj', '慧采家电家居', '专营店', 143, '李明磊', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0);

SELECT CONCAT('李明磊的VC账号创建完成，影响行数: ', ROW_COUNT()) as result;

-- 张光烁的VC账号 (ID: 144)
INSERT IGNORE INTO vc_account (account_code, account_name, account_type, manager_user_id, manager_user_name, status, priority_level, creator, create_time, updater, update_time, tenant_id, deleted)
VALUES 
('shxzy002', 'shxzy002', '专营店', 144, '张光烁', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('hcdzsw', '海程电子商务', '专营店', 144, '张光烁', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('jyjc888', '隽佑建材888', '专营店', 144, '张光烁', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('qw78899', '千唯78899', '专营店', 144, '张光烁', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('mc459', '米层459', '专营店', 144, '张光烁', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('mchwhc', '米层户外慧采', '专营店', 144, '张光烁', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('fld3c', '福莱达3C数码', '专营店', 144, '张光烁', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('flddsf', '福莱达大时尚', '专营店', 144, '张光烁', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('mcjdjj', '米层家电家居', '专营店', 144, '张光烁', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0);

SELECT CONCAT('张光烁的VC账号创建完成，影响行数: ', ROW_COUNT()) as result;

-- 郭梦含的VC账号 (ID: 145)
INSERT IGNORE INTO vc_account (account_code, account_name, account_type, manager_user_id, manager_user_name, status, priority_level, creator, create_time, updater, update_time, tenant_id, deleted)
VALUES 
('sfldsc', '赛帆禄大商超', '专营店', 145, '郭梦含', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('sfldqc', '赛帆禄大汽车', '专营店', 145, '郭梦含', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('sfl3c', '赛帆禄3C', '专营店', 145, '郭梦含', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('lwqchc', '莱维汽车慧采', '专营店', 145, '郭梦含', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('ldqchc', '麟道汽车慧采', '专营店', 145, '郭梦含', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('fqqchc', '丰谦汽车慧采', '专营店', 145, '郭梦含', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('shld', '上海麟道', '专营店', 145, '郭梦含', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('lbhnzhc', '隆博海农资慧采', '专营店', 145, '郭梦含', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('hnsflqc', '河南赛帆禄汽车', '专营店', 145, '郭梦含', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('yhtccp', '云汉天成车品供应', '专营店', 145, '郭梦含', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0);

SELECT CONCAT('郭梦含的VC账号创建完成，影响行数: ', ROW_COUNT()) as result;

-- 孟雪倩的VC账号 (ID: 146) - 暂时没有具体账号，先跳过

-- 曹嘉处的VC账号 (ID: 147)
INSERT IGNORE INTO vc_account (account_code, account_name, account_type, manager_user_id, manager_user_name, status, priority_level, creator, create_time, updater, update_time, tenant_id, deleted)
VALUES 
('ht9988166', '海棠9988166', '专营店', 147, '曹嘉处', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('hnzy', '河南赞一', '专营店', 147, '曹嘉处', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('hngs', '河南钢隋', '专营店', 147, '曹嘉处', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('hndlhc', '河南迪黎慧采', '专营店', 147, '曹嘉处', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('shsla6688', '上海圣兰奥6688', '专营店', 147, '曹嘉处', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('hndl', '河南迪黎', '专营店', 147, '曹嘉处', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('gsqcsyhc', '钢隋汽车事业部慧采', '专营店', 147, '曹嘉处', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('slajj', '圣兰奥家居', '专营店', 147, '曹嘉处', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('qbzqchc', '齐步走汽车慧采', '专营店', 147, '曹嘉处', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0),
('hnzyqchc', '河南赞一汽车慧采', '专营店', 147, '曹嘉处', 1, 2, 'system', NOW(), 'system', NOW(), 1, 0);

SELECT CONCAT('曹嘉处的VC账号创建完成，影响行数: ', ROW_COUNT()) as result;

-- ========================================
-- 步骤3: 验证创建结果
-- ========================================
SELECT '=== 验证创建结果 ===' as message;
SELECT 
    account_code,
    account_name,
    manager_user_id,
    manager_user_name,
    status,
    create_time
FROM vc_account 
WHERE creator = 'system' 
  AND DATE(create_time) = CURDATE()
  AND deleted = 0
ORDER BY manager_user_name, account_code;

-- ========================================
-- 步骤4: 统计结果
-- ========================================
SELECT '=== 统计每个负责人的账号数量 ===' as message;
SELECT 
    manager_user_name,
    COUNT(*) as account_count
FROM vc_account 
WHERE creator = 'system' 
  AND DATE(create_time) = CURDATE()
  AND deleted = 0
GROUP BY manager_user_name
ORDER BY manager_user_name;

SELECT '=== 总计创建的VC账号数量 ===' as message;
SELECT COUNT(*) as total_created_accounts
FROM vc_account 
WHERE creator = 'system' 
  AND DATE(create_time) = CURDATE()
  AND deleted = 0;
