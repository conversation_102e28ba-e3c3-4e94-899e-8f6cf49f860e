-- 修复VC账号资质数量统计问题的SQL脚本
-- 解决方案：确保数据库表结构正确，并提供统计查询

-- ========================================
-- 步骤1: 检查当前表结构
-- ========================================
SELECT 'VC账号表结构检查' as message;
DESCRIBE vc_account;

SELECT 'qualification_summary表结构检查' as message;
DESCRIBE qualification_summary;

-- 检查是否存在vc_account_id字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'qualification_summary' 
  AND COLUMN_NAME IN ('vc_account', 'vc_account_id');

-- ========================================
-- 步骤2: 添加vc_account_id字段（如果不存在）
-- ========================================
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'qualification_summary' 
      AND COLUMN_NAME = 'vc_account_id'
);

-- 如果字段不存在，则添加
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE qualification_summary ADD COLUMN vc_account_id bigint(20) DEFAULT NULL COMMENT "VC账号ID（关联vc_account表）" AFTER vc_account',
    'SELECT "vc_account_id字段已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果不存在）
SET @index_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'qualification_summary' 
      AND INDEX_NAME = 'idx_vc_account_id'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qualification_summary ADD INDEX idx_vc_account_id (vc_account_id)',
    'SELECT "idx_vc_account_id索引已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 步骤3: 数据关联修复
-- ========================================
-- 更新qualification_summary表的vc_account_id字段
-- 通过vc_account字符串字段关联到vc_account表的account_code字段

UPDATE qualification_summary qs
JOIN vc_account va ON qs.vc_account = va.account_code
SET qs.vc_account_id = va.id
WHERE qs.vc_account_id IS NULL 
  AND qs.vc_account IS NOT NULL 
  AND qs.vc_account != ''
  AND qs.deleted = 0
  AND va.deleted = 0;

-- ========================================
-- 步骤4: 验证数据关联结果
-- ========================================
SELECT 
    '数据关联统计' as message,
    COUNT(*) as total_qualifications,
    COUNT(vc_account_id) as linked_qualifications,
    COUNT(*) - COUNT(vc_account_id) as unlinked_qualifications
FROM qualification_summary 
WHERE deleted = 0;

-- 显示未关联的记录
SELECT 
    'VC账号未关联的资质记录' as message,
    vc_account,
    COUNT(*) as count
FROM qualification_summary 
WHERE deleted = 0 
  AND vc_account_id IS NULL
  AND vc_account IS NOT NULL
GROUP BY vc_account
ORDER BY count DESC
LIMIT 10;

-- ========================================
-- 步骤5: 统计每个VC账号的资质数量
-- ========================================
SELECT 
    'VC账号资质数量统计' as message;

SELECT 
    va.id,
    va.account_code,
    va.account_name,
    COUNT(qs.id) as qualification_count,
    COUNT(CASE WHEN qs.qualification_expire_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY) THEN 1 END) as expiring_count
FROM vc_account va
LEFT JOIN qualification_summary qs ON va.id = qs.vc_account_id AND qs.deleted = 0
WHERE va.deleted = 0
GROUP BY va.id, va.account_code, va.account_name
ORDER BY qualification_count DESC
LIMIT 20;

-- ========================================
-- 步骤6: 创建统计视图（可选）
-- ========================================
CREATE OR REPLACE VIEW v_vc_account_with_stats AS
SELECT 
    va.*,
    COALESCE(stats.qualification_count, 0) as qualification_count,
    COALESCE(stats.expiring_count, 0) as expiring_count
FROM vc_account va
LEFT JOIN (
    SELECT 
        vc_account_id,
        COUNT(*) as qualification_count,
        COUNT(CASE WHEN qualification_expire_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY) THEN 1 END) as expiring_count
    FROM qualification_summary 
    WHERE deleted = 0
    GROUP BY vc_account_id
) stats ON va.id = stats.vc_account_id
WHERE va.deleted = 0;

SELECT 'v_vc_account_with_stats视图创建完成' as message;

-- ========================================
-- 步骤7: 测试统计查询
-- ========================================
SELECT 'VC账号统计测试查询' as message;

SELECT 
    id,
    account_code,
    account_name,
    qualification_count,
    expiring_count
FROM v_vc_account_with_stats
ORDER BY qualification_count DESC
LIMIT 10;
