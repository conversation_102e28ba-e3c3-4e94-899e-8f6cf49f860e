import type { VxeGridPropTypes } from 'vxe-table';
import type { VbenFormSchema } from '@vben/common-ui';

import { h } from 'vue';

import { ElTag } from 'element-plus';

import { formatDate } from '@vben/utils';

/**
 * 表格列配置
 */
export function useGridColumns(): VxeGridPropTypes.Columns {
  return [
    { type: 'checkbox', width: 60 },
    {
      title: 'VC账号',
      field: 'vcAccount',
      minWidth: 150,
      showOverflow: true,
    },
    {
      title: '品牌',
      field: 'brand',
      minWidth: 120,
      showOverflow: true,
    },
    {
      title: '一级分类',
      field: 'firstCategory',
      minWidth: 120,
      showOverflow: true,
    },
    {
      title: '二级分类',
      field: 'secondCategory',
      minWidth: 120,
      showOverflow: true,
    },
    {
      title: '三级分类',
      field: 'thirdCategory',
      minWidth: 120,
      showOverflow: true,
    },
    {
      title: '产品线类型',
      field: 'productLineType',
      minWidth: 120,
    },
    {
      title: '一级部门',
      field: 'firstDepartment',
      minWidth: 150,
      showOverflow: true,
    },
    {
      title: '采销员',
      field: 'purchaser',
      minWidth: 100,
    },
    {
      title: '采销员姓名',
      field: 'purchaserName',
      minWidth: 100,
    },
    {
      title: '产品线分级',
      field: 'productLineLevel',
      minWidth: 150,
      showOverflow: true,
    },
    {
      title: '资质到期时间',
      field: 'qualificationExpireDate',
      minWidth: 150,
      formatter: ({ cellValue }) => {
        return cellValue ? formatDate(cellValue) : '';
      },
    },
    {
      title: '资质状态',
      field: 'expiredStatus',
      width: 120,
      slots: {
        default: ({ row }) => {
          const getStatusConfig = (status: number) => {
            switch (status) {
              case 0:
                return { type: 'danger' as const, text: '已过期' };
              case 1:
                return { type: 'success' as const, text: '正常' };
              case 2:
                return { type: 'warning' as const, text: '即将过期' };
              default:
                return { type: 'info' as const, text: '未知' };
            }
          };
          const config = getStatusConfig(row.expiredStatus);
          return h(ElTag, {
            type: config.type,
          }, () => config.text);
        },
      },
    },
    {
      title: '状态',
      field: 'status',
      width: 100,
      slots: {
        default: ({ row }) => {
          return h(ElTag, {
            type: row.status === 1 ? 'success' : 'danger',
          }, () => row.status === 1 ? '正常' : '停用');
        },
      },
    },
    {
      title: '操作',
      field: 'actions',
      width: 180,
      fixed: 'right',
      slots: { default: 'actions' },
    },
  ];
}

/**
 * 搜索表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入VC账号',
        clearable: true,
      },
      fieldName: 'vcAccount',
      label: 'VC账号',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择VC账号',
        clearable: true,
        api: async () => {
          // 调用VC账号简单列表API
          const response = await fetch('/qualification/vc-account/simple-list');
          const result = await response.json();
          return result.data?.map((item: any) => ({
            label: `${item.accountName} (${item.accountCode})`,
            value: item.id,
          })) || [];
        },
        labelField: 'label',
        valueField: 'value',
      },
      fieldName: 'vcAccountId',
      label: 'VC账号选择',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入品牌',
        clearable: true,
      },
      fieldName: 'brand',
      label: '品牌',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入采销员',
        clearable: true,
      },
      fieldName: 'purchaser',
      label: '采销员',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入采销员姓名',
        clearable: true,
      },
      fieldName: 'purchaserName',
      label: '采销员姓名',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        clearable: true,
        options: [
          { label: '正常', value: 1 },
          { label: '停用', value: 0 },
        ],
      },
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择资质状态',
        clearable: true,
        options: [
          { label: '正常', value: 1 },
          { label: '已过期', value: 0 },
          { label: '即将过期', value: 2 },
        ],
      },
      fieldName: 'expiredStatus',
      label: '资质状态',
    },
  ];
}

export function useFormSchema() {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入VC账号',
      },
      fieldName: 'vcAccount',
      label: 'VC账号',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入品牌',
      },
      fieldName: 'brand',
      label: '品牌',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入一级分类',
      },
      fieldName: 'firstCategory',
      label: '一级分类',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入二级分类',
      },
      fieldName: 'secondCategory',
      label: '二级分类',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入三级分类',
      },
      fieldName: 'thirdCategory',
      label: '三级分类',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择产品线类型',
        options: [
          { label: '标准产品线', value: 'standard' },
          { label: '定制产品线', value: 'custom' },
          { label: '特殊产品线', value: 'special' },
        ],
      },
      fieldName: 'productLineType',
      label: '产品线类型',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入一级部门',
      },
      fieldName: 'firstDepartment',
      label: '一级部门',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入采销员',
      },
      fieldName: 'purchaser',
      label: '采销员',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择产品线分级',
        options: [
          { label: 'A级', value: 'A' },
          { label: 'B级', value: 'B' },
          { label: 'C级', value: 'C' },
        ],
      },
      fieldName: 'productLineLevel',
      label: '产品线分级',
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择资质到期时间',
        type: 'date',
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'qualificationExpireDate',
      label: '资质到期时间',
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '正常', value: 1 },
          { label: '停用', value: 0 },
        ],
      },
      fieldName: 'status',
      label: '状态',
      defaultValue: 1,
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入备注',
        rows: 3,
      },
      fieldName: 'remark',
      label: '备注',
    },
  ];
}

export function useImportFormSchema() {
  return [
    {
      component: 'Upload',
      fieldName: 'file',
      label: '导入文件',
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
      fieldName: 'updateSupport',
      label: '是否更新已经存在的数据',
      defaultValue: false,
    },
  ];
}
