package cn.iocoder.yudao.module.qualification.convert;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.qualification.controller.admin.vcaccount.vo.VcAccountRespVO;
import cn.iocoder.yudao.module.qualification.controller.admin.vcaccount.vo.VcAccountSaveReqVO;
import cn.iocoder.yudao.module.qualification.dal.dataobject.VcAccountDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:45:09+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class VcAccountConvertImpl implements VcAccountConvert {

    @Override
    public VcAccountDO convert(VcAccountSaveReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        VcAccountDO.VcAccountDOBuilder vcAccountDO = VcAccountDO.builder();

        vcAccountDO.accountCode( bean.getAccountCode() );
        vcAccountDO.accountName( bean.getAccountName() );
        vcAccountDO.accountType( bean.getAccountType() );
        vcAccountDO.backupManagerUserId( bean.getBackupManagerUserId() );
        vcAccountDO.businessScope( bean.getBusinessScope() );
        vcAccountDO.companyName( bean.getCompanyName() );
        vcAccountDO.contactEmail( bean.getContactEmail() );
        vcAccountDO.contactPerson( bean.getContactPerson() );
        vcAccountDO.contactPhone( bean.getContactPhone() );
        vcAccountDO.id( bean.getId() );
        vcAccountDO.managerUserId( bean.getManagerUserId() );
        vcAccountDO.priorityLevel( bean.getPriorityLevel() );
        vcAccountDO.regionCode( bean.getRegionCode() );
        vcAccountDO.regionName( bean.getRegionName() );
        vcAccountDO.remark( bean.getRemark() );
        vcAccountDO.status( bean.getStatus() );

        return vcAccountDO.build();
    }

    @Override
    public VcAccountRespVO convert(VcAccountDO bean) {
        if ( bean == null ) {
            return null;
        }

        VcAccountRespVO vcAccountRespVO = new VcAccountRespVO();

        vcAccountRespVO.setAccountCode( bean.getAccountCode() );
        vcAccountRespVO.setAccountName( bean.getAccountName() );
        vcAccountRespVO.setAccountType( bean.getAccountType() );
        vcAccountRespVO.setBackupManagerUserId( bean.getBackupManagerUserId() );
        vcAccountRespVO.setBackupManagerUserName( bean.getBackupManagerUserName() );
        vcAccountRespVO.setBusinessScope( bean.getBusinessScope() );
        vcAccountRespVO.setCompanyName( bean.getCompanyName() );
        vcAccountRespVO.setContactEmail( bean.getContactEmail() );
        vcAccountRespVO.setContactPerson( bean.getContactPerson() );
        vcAccountRespVO.setContactPhone( bean.getContactPhone() );
        vcAccountRespVO.setCreateTime( bean.getCreateTime() );
        vcAccountRespVO.setCreator( bean.getCreator() );
        vcAccountRespVO.setExpiringCount( bean.getExpiringCount() );
        vcAccountRespVO.setId( bean.getId() );
        vcAccountRespVO.setManagerUserId( bean.getManagerUserId() );
        vcAccountRespVO.setManagerUserName( bean.getManagerUserName() );
        vcAccountRespVO.setPriorityLevel( bean.getPriorityLevel() );
        vcAccountRespVO.setQualificationCount( bean.getQualificationCount() );
        vcAccountRespVO.setRegionCode( bean.getRegionCode() );
        vcAccountRespVO.setRegionName( bean.getRegionName() );
        vcAccountRespVO.setRemark( bean.getRemark() );
        vcAccountRespVO.setStatus( bean.getStatus() );
        vcAccountRespVO.setUpdateTime( bean.getUpdateTime() );
        vcAccountRespVO.setUpdater( bean.getUpdater() );

        return vcAccountRespVO;
    }

    @Override
    public List<VcAccountRespVO> convertList(List<VcAccountDO> list) {
        if ( list == null ) {
            return null;
        }

        List<VcAccountRespVO> list1 = new ArrayList<VcAccountRespVO>( list.size() );
        for ( VcAccountDO vcAccountDO : list ) {
            list1.add( convert( vcAccountDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<VcAccountRespVO> convertPage(PageResult<VcAccountDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<VcAccountRespVO> pageResult = new PageResult<VcAccountRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }
}
