<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <ImportModal @success="onRefresh" />

    <Grid table-title="资质管理列表">
      <template #toolbar-tools>
        <TableAction
          :actions="[
            {
              label: $t('ui.actionTitle.create', ['资质管理']),
              type: 'primary',
              icon: ACTION_ICON.ADD,
              auth: ['qualification:summary:create'],
              onClick: onCreate,
            },
            {
              label: $t('ui.actionTitle.export'),
              type: 'primary',
              icon: ACTION_ICON.DOWNLOAD,
              auth: ['qualification:summary:export'],
              onClick: onExport,
            },
            {
              label: $t('ui.actionTitle.import', ['资质管理']),
              type: 'primary',
              icon: ACTION_ICON.UPLOAD,
              auth: ['qualification:summary:import'],
              onClick: onImport,
            },
            {
              label: '下载模板',
              type: 'primary',
              icon: ACTION_ICON.DOWNLOAD,
              onClick: onDownloadTemplate,
            },
            {
              label: '测试推送',
              type: 'primary',
              icon: ACTION_ICON.UPLOAD,
              // auth: ['qualification:summary:test-push'], // 临时移除权限控制用于测试
              onClick: onTestPush,
            },
          ]"
        />
      </template>
      <template #actions="{ row }">
        <TableAction
          :actions="[
            {
              label: $t('common.edit'),
              type: 'text',
              icon: ACTION_ICON.EDIT,
              auth: ['qualification:summary:update'],
              onClick: onEdit.bind(null, row),
            },
            {
              label: $t('common.delete'),
              type: 'danger',
              text: true,
              icon: ACTION_ICON.DELETE,
              auth: ['qualification:summary:delete'],
              popConfirm: {
                title: $t('ui.actionMessage.deleteConfirm', [row.vcAccount]),
                confirm: onDelete.bind(null, row),
              },
            },
          ]"
        />
      </template>
    </Grid>
  </Page>
</template>

<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromBlobPart } from '@vben/utils';

import { ElLoading, ElMessage } from 'element-plus';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import { $t } from '#/locales';

import { requestClient } from '#/api/request';

import { useGridColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';
import ImportForm from './modules/import-form.vue';

// 定义接口类型
interface QualificationSummary {
  id?: number
  vcAccount: string
  brand: string
  brandCode?: string
  firstCategory: string
  firstCategoryCode?: string
  secondCategory: string
  secondCategoryCode?: string
  thirdCategory: string
  thirdCategoryCode?: string
  productLineType: string
  firstDepartment: string
  firstDepartmentCode?: string
  purchaser: string
  purchaserName?: string
  productLineLevel: string
  qualificationExpireDate: string
  status: number
  expiredStatus: number
  remark?: string
  createTime?: string
  updateTime?: string
}

// API 函数
const getQualificationSummaryPage = async (params: any) => {
  return await requestClient.get('/qualification/summary/page', { params })
}

const deleteQualificationSummary = async (id: number) => {
  return await requestClient.delete(`/qualification/summary/delete?id=${id}`)
}

const exportQualificationSummary = async (params: any) => {
  const response = await requestClient.get('/qualification/summary/export-excel', {
    params,
    responseType: 'blob'
  })
  return response
}

const downloadImportTemplate = async () => {
  const response = await requestClient.get('/qualification/summary/get-import-template', {
    responseType: 'blob'
  })
  return response
}

const testPushQualificationReminder = async () => {
  return await requestClient.post('/qualification/test/push')
}


const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

const [ImportModal, importModalApi] = useVbenModal({
  connectedComponent: ImportForm,
  destroyOnClose: true,
});

/** 刷新表格 */
function onRefresh() {
  gridApi.query();
}

/** 导出表格 */
async function onExport() {
  const data = await exportQualificationSummary(await gridApi.formApi.getValues());
  downloadFileFromBlobPart({ fileName: '资质管理.xlsx', source: data });
}

/** 导入资质管理 */
function onImport() {
  importModalApi.open();
}

/** 下载模板 */
async function onDownloadTemplate() {
  const data = await downloadImportTemplate();
  downloadFileFromBlobPart({ fileName: '资质管理导入模板.xlsx', source: data });
}

/** 测试推送 */
async function onTestPush() {
  let loadingInstance = null;
  try {
    // 显示加载提示
    loadingInstance = ElLoading.service({
      lock: true,
      text: '正在执行测试推送...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 调用测试推送API
    const response = await testPushQualificationReminder();

    // 显示结果
    if (response.data && response.data.success) {
      ElMessage.success(`测试推送执行成功！目标用户ID: ${response.data.targetUserId}, 模板: ${response.data.templateCode}`);
    } else {
      ElMessage.error(`测试推送执行失败: ${response.data?.message || '未知错误'}`);
    }

    console.log('测试推送结果:', response);

  } catch (error) {
    console.error('测试推送失败:', error);
    ElMessage.error('测试推送执行失败，请检查控制台日志');
  } finally {
    // 确保关闭加载提示
    if (loadingInstance) {
      loadingInstance.close();
    }
  }
}

/** 创建资质管理 */
function onCreate() {
  formModalApi.setData({}).open();
}

/** 编辑资质管理 */
function onEdit(row: QualificationSummary) {
  formModalApi.setData(row).open();
}

/** 删除资质管理 */
async function onDelete(row: QualificationSummary) {
  const loadingInstance = ElLoading.service({
    text: $t('ui.actionMessage.deleting', [row.vcAccount]),
    fullscreen: true,
  });
  try {
    await deleteQualificationSummary(row.id as number);
    ElMessage.success($t('ui.actionMessage.deleteSuccess', [row.vcAccount]));
    onRefresh();
  } catch {
    // 异常处理
  } finally {
    loadingInstance.close();
  }
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getQualificationSummaryPage({
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      refresh: { code: 'query' },
      search: true,
    },
  } as VxeTableGridOptions<QualificationSummary>,
});
</script>