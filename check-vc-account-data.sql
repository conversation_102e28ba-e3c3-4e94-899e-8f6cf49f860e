-- 检查VC账号和资质数据关联情况的SQL脚本

-- ========================================
-- 步骤1: 检查表结构和数据
-- ========================================
SELECT 'VC账号表数据统计' as message;
SELECT 
    COUNT(*) as total_vc_accounts,
    COUNT(CASE WHEN status = 1 THEN 1 END) as active_accounts,
    COUNT(CASE WHEN status = 0 THEN 1 END) as inactive_accounts
FROM vc_account 
WHERE deleted = 0;

SELECT 'qualification_summary表数据统计' as message;
SELECT 
    COUNT(*) as total_qualifications,
    COUNT(vc_account_id) as with_vc_account_id,
    COUNT(*) - COUNT(vc_account_id) as without_vc_account_id,
    COUNT(DISTINCT vc_account) as unique_vc_accounts_str,
    COUNT(DISTINCT vc_account_id) as unique_vc_account_ids
FROM qualification_summary 
WHERE deleted = 0;

-- ========================================
-- 步骤2: 检查数据关联情况
-- ========================================
SELECT 'VC账号字符串与ID关联检查' as message;

-- 检查有多少vc_account字符串能匹配到vc_account表
SELECT 
    'vc_account字符串匹配情况' as check_type,
    COUNT(DISTINCT qs.vc_account) as total_unique_vc_accounts,
    COUNT(DISTINCT CASE WHEN va.id IS NOT NULL THEN qs.vc_account END) as matched_vc_accounts,
    COUNT(DISTINCT CASE WHEN va.id IS NULL THEN qs.vc_account END) as unmatched_vc_accounts
FROM qualification_summary qs
LEFT JOIN vc_account va ON qs.vc_account = va.account_code AND va.deleted = 0
WHERE qs.deleted = 0 
  AND qs.vc_account IS NOT NULL 
  AND qs.vc_account != '';

-- 显示未匹配的vc_account
SELECT '未匹配的VC账号字符串' as message;
SELECT 
    qs.vc_account,
    COUNT(*) as qualification_count
FROM qualification_summary qs
LEFT JOIN vc_account va ON qs.vc_account = va.account_code AND va.deleted = 0
WHERE qs.deleted = 0 
  AND qs.vc_account IS NOT NULL 
  AND qs.vc_account != ''
  AND va.id IS NULL
GROUP BY qs.vc_account
ORDER BY qualification_count DESC
LIMIT 10;

-- ========================================
-- 步骤3: 修复数据关联（如果需要）
-- ========================================
SELECT 'VC账号ID关联修复' as message;

-- 更新qualification_summary表的vc_account_id字段
UPDATE qualification_summary qs
JOIN vc_account va ON qs.vc_account = va.account_code
SET qs.vc_account_id = va.id
WHERE qs.vc_account_id IS NULL 
  AND qs.vc_account IS NOT NULL 
  AND qs.vc_account != ''
  AND qs.deleted = 0
  AND va.deleted = 0;

SELECT ROW_COUNT() as updated_records;

-- ========================================
-- 步骤4: 验证修复结果
-- ========================================
SELECT 'VC账号资质数量统计' as message;

SELECT 
    va.id,
    va.account_code,
    va.account_name,
    va.status,
    COUNT(qs.id) as qualification_count,
    COUNT(CASE 
        WHEN qs.qualification_expire_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY) 
        THEN 1 
    END) as expiring_count
FROM vc_account va
LEFT JOIN qualification_summary qs ON va.id = qs.vc_account_id AND qs.deleted = 0
WHERE va.deleted = 0
GROUP BY va.id, va.account_code, va.account_name, va.status
ORDER BY qualification_count DESC
LIMIT 20;

-- ========================================
-- 步骤5: 检查具体的统计查询
-- ========================================
SELECT 'VC账号ID=1的资质统计测试' as message;

-- 测试统计查询（假设存在ID=1的VC账号）
SELECT 
    (SELECT COUNT(*) FROM qualification_summary WHERE vc_account_id = 1 AND deleted = 0) as qualification_count,
    (SELECT COUNT(*) FROM qualification_summary 
     WHERE vc_account_id = 1 AND deleted = 0 
       AND qualification_expire_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY)) as expiring_count;

-- 显示前几个VC账号的详细统计
SELECT '前5个VC账号的详细统计' as message;
SELECT 
    va.id,
    va.account_code,
    va.account_name,
    (SELECT COUNT(*) FROM qualification_summary 
     WHERE vc_account_id = va.id AND deleted = 0) as qualification_count,
    (SELECT COUNT(*) FROM qualification_summary 
     WHERE vc_account_id = va.id AND deleted = 0 
       AND qualification_expire_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY)) as expiring_count
FROM vc_account va
WHERE va.deleted = 0
ORDER BY va.id
LIMIT 5;

-- ========================================
-- 步骤6: 检查数据完整性
-- ========================================
SELECT 'qualification_summary表中vc_account_id为NULL的记录' as message;
SELECT 
    vc_account,
    COUNT(*) as count
FROM qualification_summary 
WHERE deleted = 0 
  AND vc_account_id IS NULL
  AND vc_account IS NOT NULL
GROUP BY vc_account
ORDER BY count DESC
LIMIT 10;
